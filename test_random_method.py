#!/usr/bin/env python3
"""
Test script to verify the random method integration in VibroML.
This script tests the basic functionality without running full calculations.
"""

import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch
import numpy as np

# Add the vibroml package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def test_random_method_cli_integration():
    """Test that the random method is properly integrated into the CLI."""
    print("Testing CLI integration for random method...")
    
    try:
        from vibroml.utils.utils import get_arg_parser_and_settings
        
        parser, settings = get_arg_parser_and_settings()
        
        # Test that 'random' is in the choices for --method
        method_action = None
        for action in parser._actions:
            if action.dest == 'method':
                method_action = action
                break
        
        if method_action is None:
            print("  ❌ Method argument not found in parser")
            return False
        
        if 'random' not in method_action.choices:
            print(f"  ❌ 'random' not found in method choices: {method_action.choices}")
            return False
        
        print("  ✅ 'random' method found in CLI choices")
        
        # Test that random-specific settings are loaded
        expected_random_settings = ['random_displacement_bounds', 'random_cell_perturbation', 'random_seed']
        for setting in expected_random_settings:
            if setting not in settings:
                print(f"  ❌ Random setting '{setting}' not found in default settings")
                return False
        
        print("  ✅ Random-specific settings found in default settings")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing CLI integration: {e}")
        return False

def test_random_structure_generation():
    """Test the random structure generation function."""
    print("Testing random structure generation...")

    try:
        from vibroml.utils.structure_utils import generate_random_displaced_structures
        from ase import Atoms

        # Create a simple test structure
        test_atoms = Atoms('H2', positions=[[0, 0, 0], [0, 0, 1]], cell=[5, 5, 5])

        with tempfile.TemporaryDirectory() as temp_dir:
            # Test parameters
            displacement_bounds = [0.1, 0.5]
            supercell_variants = [(1, 1, 1)]  # Test with single supercell as per modification

            # Generate random structures with GA-style cell transformation
            generated_files = generate_random_displaced_structures(
                test_atoms,
                displacement_bounds,
                supercell_variants,
                temp_dir,
                iteration_idx=1,
                original_prefix="test_structure",
                cell_transformation_vector=(0.1, 0.1, 0.1, 2.0, 1.0, 0.5),  # GA-style: 3 scales + 3 angles
                cell_perturbation=True,
                random_seed=42
            )

            if not generated_files:
                print("  ❌ No files generated")
                return False

            # Check that files were created
            for filepath in generated_files:
                if not os.path.exists(filepath):
                    print(f"  ❌ Generated file does not exist: {filepath}")
                    return False

            print(f"  ✅ Generated {len(generated_files)} random structure files")

            # Check filename format (should be GA-style now)
            for filepath in generated_files:
                filename = os.path.basename(filepath)
                if 'random' not in filename:
                    print(f"  ❌ Filename doesn't contain 'random': {filename}")
                    return False
                if 'c_' not in filename:  # GA-style cell transformation marker
                    print(f"  ❌ Filename doesn't contain GA-style cell marker 'c_': {filename}")
                    return False
                print(f"  ✅ Generated file with GA-style naming: {os.path.basename(filename)}")

            print("  ✅ Generated files have correct GA-style naming format")
            return True

    except Exception as e:
        print(f"  ❌ Error testing random structure generation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_random_method_routing():
    """Test that the random method is properly routed in the optimization function."""
    print("Testing random method routing...")
    
    try:
        from vibroml.auto_optimize import run_automatic_soft_mode_optimization
        
        # Create mock arguments
        mock_args = Mock()
        mock_args.method = 'random'
        mock_args.engine = 'mace'
        mock_args.units = 'THz'
        mock_args.supercell_dims = (2, 2, 2)
        mock_args.delta = 0.03
        mock_args.fmax = 0.001
        
        # Mock the run_random_structure_search function to avoid actual execution
        with patch('vibroml.auto_optimize.run_random_structure_search') as mock_random_search:
            with tempfile.TemporaryDirectory() as temp_dir:
                # Mock other required objects
                mock_atoms = Mock()
                mock_modes_info = [{'frequency': -0.5, 'label': 'test_mode'}]
                
                # Call the routing function
                run_automatic_soft_mode_optimization(
                    mock_args,
                    temp_dir,
                    best_negative_frequency=-0.5,
                    best_settings={},
                    best_softest_modes_info=mock_modes_info,
                    best_relaxed_atoms=mock_atoms,
                    negative_phonon_threshold_thz=-0.1,
                    soft_mode_max_iterations=2,
                    soft_mode_displacement_scales=[1.0],
                    mode2_ratio_scales=[0.0],
                    soft_mode_num_top_structures_to_analyze=3,
                    phonon_path_npoints=10,
                    phonon_dos_grid=(10, 10, 10),
                    default_traj_kT=1.0,
                    cell_scale_factors=[0.0],
                    num_modes_to_return=2,
                    ga_population_size=10,
                    ga_mutation_rate=0.1,
                    ga_generations=1,
                    num_new_points_per_iteration=5,
                    ga_disp_scale_bounds=(0.1, 2.0),
                    ga_ratio_bounds=(-1.0, 1.0),
                    ga_cell_scale_bounds=(-0.1, 0.1),
                    ga_cell_angle_bounds=(-5.0, 5.0)
                )
                
                # Check that the random search function was called
                if not mock_random_search.called:
                    print("  ❌ run_random_structure_search was not called")
                    return False
                
                print("  ✅ Random method properly routed to run_random_structure_search")
                
                # Check that the function was called with correct arguments
                call_args = mock_random_search.call_args
                if call_args is None:
                    print("  ❌ No call arguments found")
                    return False
                
                # Verify some key arguments
                kwargs = call_args[1]
                if 'random_displacement_bounds' not in kwargs:
                    print("  ❌ random_displacement_bounds not passed to function")
                    return False

                if 'random_cell_perturbation' not in kwargs:
                    print("  ❌ random_cell_perturbation not passed to function")
                    return False

                if 'ga_cell_scale_bounds' not in kwargs:
                    print("  ❌ ga_cell_scale_bounds not passed to function")
                    return False

                if 'ga_cell_angle_bounds' not in kwargs:
                    print("  ❌ ga_cell_angle_bounds not passed to function")
                    return False

                print("  ✅ Random-specific and GA bounds parameters properly passed to function")
                return True
                
    except Exception as e:
        print(f"  ❌ Error testing random method routing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_default_settings_integration():
    """Test that the default settings file contains random parameters."""
    print("Testing default settings integration...")
    
    try:
        from vibroml.utils.utils import load_default_settings
        
        settings = load_default_settings()
        
        # Check for random-specific settings
        expected_settings = {
            'random_displacement_bounds': list,
            'random_cell_perturbation': bool,
            'random_seed': (type(None), int)
        }
        
        for setting_name, expected_type in expected_settings.items():
            if setting_name not in settings:
                print(f"  ❌ Setting '{setting_name}' not found in default settings")
                return False
            
            setting_value = settings[setting_name]
            if not isinstance(setting_value, expected_type):
                print(f"  ❌ Setting '{setting_name}' has wrong type: {type(setting_value)}, expected: {expected_type}")
                return False
        
        print("  ✅ All random settings found with correct types")
        
        # Check specific values
        disp_bounds = settings['random_displacement_bounds']
        if not isinstance(disp_bounds, list) or len(disp_bounds) != 2:
            print(f"  ❌ random_displacement_bounds should be a list of 2 values, got: {disp_bounds}")
            return False
        
        if disp_bounds[0] >= disp_bounds[1]:
            print(f"  ❌ random_displacement_bounds should have min < max, got: {disp_bounds}")
            return False
        
        print("  ✅ Random displacement bounds have valid format")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing default settings: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_supercell_generation():
    """Test the comprehensive supercell generation logic."""
    print("Testing comprehensive supercell generation...")

    try:
        # Test the supercell generation logic directly
        def generate_comprehensive_supercell_variants(max_size=2):
            """Generate all possible supercell combinations up to max_size."""
            variants = []
            for nx in range(1, max_size + 1):
                for ny in range(1, max_size + 1):
                    for nz in range(1, max_size + 1):
                        variants.append((nx, ny, nz))
            return variants

        # Test with max_size=2
        variants = generate_comprehensive_supercell_variants(max_size=2)
        expected_variants = [
            (1,1,1), (1,1,2), (1,2,1), (1,2,2),
            (2,1,1), (2,1,2), (2,2,1), (2,2,2)
        ]

        if len(variants) != len(expected_variants):
            print(f"  ❌ Wrong number of variants: got {len(variants)}, expected {len(expected_variants)}")
            return False

        for expected in expected_variants:
            if expected not in variants:
                print(f"  ❌ Missing expected variant: {expected}")
                return False

        print(f"  ✅ Generated all {len(variants)} expected supercell variants")
        print(f"  ✅ Variants: {variants}")
        return True

    except Exception as e:
        print(f"  ❌ Error testing supercell generation: {e}")
        return False

def test_variable_displacement_bounds():
    """Test that variable displacement bounds work correctly."""
    print("Testing variable displacement bounds...")

    try:
        import random as py_random

        # Test the displacement bounds variation logic
        base_bounds = [0.1, 2.0]
        min_disp = base_bounds[0]
        base_max_disp = base_bounds[1]
        max_disp_variation = base_max_disp * 0.25

        # Generate several samples to test variation
        py_random.seed(42)  # For reproducible testing
        sample_bounds = []

        for i in range(10):
            sample_max_disp = py_random.uniform(base_max_disp - max_disp_variation,
                                              base_max_disp + max_disp_variation)
            sample_displacement_bounds = [min_disp, sample_max_disp]
            sample_bounds.append(sample_displacement_bounds)

        # Check that all samples have the same minimum
        for bounds in sample_bounds:
            if bounds[0] != min_disp:
                print(f"  ❌ Minimum displacement not consistent: {bounds[0]} != {min_disp}")
                return False

        # Check that maximum values vary
        max_values = [bounds[1] for bounds in sample_bounds]
        if len(set(max_values)) < 5:  # Should have at least 5 different values out of 10
            print(f"  ❌ Maximum displacement values not varying enough: {max_values}")
            return False

        # Check that all maximum values are within expected range
        expected_min_max = base_max_disp - max_disp_variation
        expected_max_max = base_max_disp + max_disp_variation

        for bounds in sample_bounds:
            if not (expected_min_max <= bounds[1] <= expected_max_max):
                print(f"  ❌ Maximum displacement out of range: {bounds[1]} not in [{expected_min_max}, {expected_max_max}]")
                return False

        print(f"  ✅ Variable displacement bounds working correctly")
        print(f"  ✅ Sample bounds range: [{min(max_values):.3f}, {max(max_values):.3f}]")
        return True

    except Exception as e:
        print(f"  ❌ Error testing variable displacement bounds: {e}")
        return False

def test_frequency_suffix_integration():
    """Test that the frequency suffix functionality is properly integrated."""
    print("Testing frequency suffix integration...")

    try:
        from vibroml.utils.phonon_utils import add_frequency_to_structure_filenames
        import tempfile
        import os

        with tempfile.TemporaryDirectory() as temp_dir:
            # Create mock structure files
            test_files = [
                "top_1_iter1_sample1_test_energy_m4p8264.cif",
                "top_1_iter1_sample1_test_energy_m4p8264.xyz",
                "top_1_iter1_sample1_test_energy_m4p8264_primitive.cif",
                "top_1_iter1_sample1_test_energy_m4p8264_conventional.cif"
            ]

            for filename in test_files:
                filepath = os.path.join(temp_dir, filename)
                with open(filepath, 'w') as f:
                    f.write("# Mock structure file\n")

            # Test with negative frequency
            analysis_id = "top_1_energy_m4p8264"
            softest_frequency = -2.5157
            add_frequency_to_structure_filenames(temp_dir, analysis_id, softest_frequency)

            # Check if files were renamed with frequency suffix
            files_after = os.listdir(temp_dir)
            expected_suffix = "_freqm2p5157THz"

            renamed_count = 0
            for filename in files_after:
                if expected_suffix in filename:
                    renamed_count += 1
                    print(f"  ✅ Found renamed file: {filename}")

            if renamed_count != len(test_files):
                print(f"  ❌ Expected {len(test_files)} renamed files, found {renamed_count}")
                return False

            print(f"  ✅ All {renamed_count} structure files correctly renamed with frequency suffix")

            # Test with positive frequency
            test_files_2 = [
                "unique_1_iter2_sample5_test_energy_m4p7500.cif",
                "unique_1_iter2_sample5_test_energy_m4p7500.xyz"
            ]

            for filename in test_files_2:
                filepath = os.path.join(temp_dir, filename)
                with open(filepath, 'w') as f:
                    f.write("# Mock structure file\n")

            analysis_id_2 = "unique_1_energy_m4p7500"
            softest_frequency_2 = 1.2345
            add_frequency_to_structure_filenames(temp_dir, analysis_id_2, softest_frequency_2)

            # Check positive frequency suffix
            files_after_2 = os.listdir(temp_dir)
            expected_suffix_2 = "_freqp1p2345THz"

            positive_renamed_count = 0
            for filename in files_after_2:
                if expected_suffix_2 in filename:
                    positive_renamed_count += 1
                    print(f"  ✅ Found positive frequency file: {filename}")

            if positive_renamed_count != len(test_files_2):
                print(f"  ❌ Expected {len(test_files_2)} positive frequency files, found {positive_renamed_count}")
                return False

            print(f"  ✅ Positive frequency suffix working correctly")
            return True

    except Exception as e:
        print(f"  ❌ Error testing frequency suffix integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_exploration_refinement_phases():
    """Test the exploration vs refinement phase logic."""
    print("Testing exploration vs refinement phases...")

    try:
        # Test the phase logic
        max_iterations = 3
        num_new_points_per_iteration = 5

        expected_phases = []
        expected_samples = []

        for iteration_idx in range(1, max_iterations + 1):
            is_exploration_phase = (iteration_idx == 1)
            if is_exploration_phase:
                samples_this_iteration = 2 * num_new_points_per_iteration
                phase_name = "EXPLORATION"
            else:
                samples_this_iteration = num_new_points_per_iteration
                phase_name = "REFINEMENT"

            expected_phases.append(phase_name)
            expected_samples.append(samples_this_iteration)

        # Check expected results
        if expected_phases != ["EXPLORATION", "REFINEMENT", "REFINEMENT"]:
            print(f"  ❌ Wrong phase sequence: {expected_phases}")
            return False

        if expected_samples != [10, 5, 5]:  # 2x5, 5, 5
            print(f"  ❌ Wrong sample counts: {expected_samples}")
            return False

        print(f"  ✅ Phase sequence correct: {expected_phases}")
        print(f"  ✅ Sample counts correct: {expected_samples}")

        # Test supercell variant cycling in exploration phase
        supercell_variants = [(1,1,1), (1,1,2), (1,2,1), (1,2,2), (2,1,1), (2,1,2), (2,2,1), (2,2,2)]
        samples_in_exploration = 10  # 2 * num_new_points_per_iteration

        expected_supercells = []
        for sample_idx in range(samples_in_exploration):
            variant_index = sample_idx % len(supercell_variants)
            expected_supercells.append(supercell_variants[variant_index])

        # Should cycle through variants: (1,1,1), (1,1,2), ..., (2,2,1), (2,2,2), (1,1,1), (1,1,2)
        expected_first_10 = [
            (1,1,1), (1,1,2), (1,2,1), (1,2,2), (2,1,1),
            (2,1,2), (2,2,1), (2,2,2), (1,1,1), (1,1,2)
        ]

        if expected_supercells != expected_first_10:
            print(f"  ❌ Wrong supercell cycling: {expected_supercells}")
            return False

        print(f"  ✅ Supercell variant cycling correct in exploration phase")
        print(f"  ✅ First 10 samples use: {expected_supercells}")

        return True

    except Exception as e:
        print(f"  ❌ Error testing exploration/refinement phases: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing Random Method Integration for VibroML")
    print("=" * 60)

    tests = [
        test_random_method_cli_integration,
        test_default_settings_integration,
        test_random_structure_generation,
        test_random_method_routing,
        test_comprehensive_supercell_generation,
        test_variable_displacement_bounds,
        test_frequency_suffix_integration,
        test_exploration_refinement_phases,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        print(f"\n{test_func.__name__.replace('_', ' ').title()}:")
        try:
            if test_func():
                passed += 1
                print("  ✅ PASSED")
            else:
                print("  ❌ FAILED")
        except Exception as e:
            print(f"  ❌ FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! Random method integration is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
