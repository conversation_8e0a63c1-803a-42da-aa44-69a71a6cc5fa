#!/usr/bin/env python3
"""
Test script to verify the random method integration in VibroML.
This script tests the basic functionality without running full calculations.
"""

import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch
import numpy as np

# Add the vibroml package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def test_random_method_cli_integration():
    """Test that the random method is properly integrated into the CLI."""
    print("Testing CLI integration for random method...")
    
    try:
        from vibroml.utils.utils import get_arg_parser_and_settings
        
        parser, settings = get_arg_parser_and_settings()
        
        # Test that 'random' is in the choices for --method
        method_action = None
        for action in parser._actions:
            if action.dest == 'method':
                method_action = action
                break
        
        if method_action is None:
            print("  ❌ Method argument not found in parser")
            return False
        
        if 'random' not in method_action.choices:
            print(f"  ❌ 'random' not found in method choices: {method_action.choices}")
            return False
        
        print("  ✅ 'random' method found in CLI choices")
        
        # Test that random-specific settings are loaded
        expected_random_settings = ['random_displacement_bounds', 'random_cell_perturbation', 'random_seed']
        for setting in expected_random_settings:
            if setting not in settings:
                print(f"  ❌ Random setting '{setting}' not found in default settings")
                return False
        
        print("  ✅ Random-specific settings found in default settings")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing CLI integration: {e}")
        return False

def test_random_structure_generation():
    """Test the random structure generation function."""
    print("Testing random structure generation...")
    
    try:
        from vibroml.utils.structure_utils import generate_random_displaced_structures
        from ase import Atoms
        
        # Create a simple test structure
        test_atoms = Atoms('H2', positions=[[0, 0, 0], [0, 0, 1]], cell=[5, 5, 5])
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test parameters
            displacement_bounds = [0.1, 0.5]
            supercell_variants = [(1, 1, 1), (2, 2, 2)]
            
            # Generate random structures
            generated_files = generate_random_displaced_structures(
                test_atoms,
                displacement_bounds,
                supercell_variants,
                temp_dir,
                iteration_idx=1,
                original_prefix="test_structure",
                cell_transformation_vector=(0.1, 0.1, 0.1, 0.0, 0.0, 0.0),
                cell_perturbation=True,
                random_seed=42
            )
            
            if not generated_files:
                print("  ❌ No files generated")
                return False
            
            # Check that files were created
            for filepath in generated_files:
                if not os.path.exists(filepath):
                    print(f"  ❌ Generated file does not exist: {filepath}")
                    return False
            
            print(f"  ✅ Generated {len(generated_files)} random structure files")
            
            # Check filename format
            for filepath in generated_files:
                filename = os.path.basename(filepath)
                if 'random' not in filename:
                    print(f"  ❌ Filename doesn't contain 'random': {filename}")
                    return False
                if 'disp_' not in filename:
                    print(f"  ❌ Filename doesn't contain displacement info: {filename}")
                    return False
            
            print("  ✅ Generated files have correct naming format")
            return True
            
    except Exception as e:
        print(f"  ❌ Error testing random structure generation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_random_method_routing():
    """Test that the random method is properly routed in the optimization function."""
    print("Testing random method routing...")
    
    try:
        from vibroml.auto_optimize import run_automatic_soft_mode_optimization
        
        # Create mock arguments
        mock_args = Mock()
        mock_args.method = 'random'
        mock_args.engine = 'mace'
        mock_args.units = 'THz'
        mock_args.supercell_dims = (2, 2, 2)
        mock_args.delta = 0.03
        mock_args.fmax = 0.001
        
        # Mock the run_random_structure_search function to avoid actual execution
        with patch('vibroml.auto_optimize.run_random_structure_search') as mock_random_search:
            with tempfile.TemporaryDirectory() as temp_dir:
                # Mock other required objects
                mock_atoms = Mock()
                mock_modes_info = [{'frequency': -0.5, 'label': 'test_mode'}]
                
                # Call the routing function
                run_automatic_soft_mode_optimization(
                    mock_args,
                    temp_dir,
                    best_negative_frequency=-0.5,
                    best_settings={},
                    best_softest_modes_info=mock_modes_info,
                    best_relaxed_atoms=mock_atoms,
                    negative_phonon_threshold_thz=-0.1,
                    soft_mode_max_iterations=2,
                    soft_mode_displacement_scales=[1.0],
                    mode2_ratio_scales=[0.0],
                    soft_mode_num_top_structures_to_analyze=3,
                    phonon_path_npoints=10,
                    phonon_dos_grid=(10, 10, 10),
                    default_traj_kT=1.0,
                    cell_scale_factors=[0.0],
                    num_modes_to_return=2,
                    ga_population_size=10,
                    ga_mutation_rate=0.1,
                    ga_generations=1,
                    num_new_points_per_iteration=5,
                    ga_disp_scale_bounds=(0.1, 2.0),
                    ga_ratio_bounds=(-1.0, 1.0),
                    ga_cell_scale_bounds=(-0.1, 0.1),
                    ga_cell_angle_bounds=(-5.0, 5.0)
                )
                
                # Check that the random search function was called
                if not mock_random_search.called:
                    print("  ❌ run_random_structure_search was not called")
                    return False
                
                print("  ✅ Random method properly routed to run_random_structure_search")
                
                # Check that the function was called with correct arguments
                call_args = mock_random_search.call_args
                if call_args is None:
                    print("  ❌ No call arguments found")
                    return False
                
                # Verify some key arguments
                kwargs = call_args[1]
                if 'random_displacement_bounds' not in kwargs:
                    print("  ❌ random_displacement_bounds not passed to function")
                    return False
                
                if 'random_cell_perturbation' not in kwargs:
                    print("  ❌ random_cell_perturbation not passed to function")
                    return False
                
                print("  ✅ Random-specific parameters properly passed to function")
                return True
                
    except Exception as e:
        print(f"  ❌ Error testing random method routing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_default_settings_integration():
    """Test that the default settings file contains random parameters."""
    print("Testing default settings integration...")
    
    try:
        from vibroml.utils.utils import load_default_settings
        
        settings = load_default_settings()
        
        # Check for random-specific settings
        expected_settings = {
            'random_displacement_bounds': list,
            'random_cell_perturbation': bool,
            'random_seed': (type(None), int)
        }
        
        for setting_name, expected_type in expected_settings.items():
            if setting_name not in settings:
                print(f"  ❌ Setting '{setting_name}' not found in default settings")
                return False
            
            setting_value = settings[setting_name]
            if not isinstance(setting_value, expected_type):
                print(f"  ❌ Setting '{setting_name}' has wrong type: {type(setting_value)}, expected: {expected_type}")
                return False
        
        print("  ✅ All random settings found with correct types")
        
        # Check specific values
        disp_bounds = settings['random_displacement_bounds']
        if not isinstance(disp_bounds, list) or len(disp_bounds) != 2:
            print(f"  ❌ random_displacement_bounds should be a list of 2 values, got: {disp_bounds}")
            return False
        
        if disp_bounds[0] >= disp_bounds[1]:
            print(f"  ❌ random_displacement_bounds should have min < max, got: {disp_bounds}")
            return False
        
        print("  ✅ Random displacement bounds have valid format")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing default settings: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing Random Method Integration for VibroML")
    print("=" * 60)
    
    tests = [
        test_random_method_cli_integration,
        test_default_settings_integration,
        test_random_structure_generation,
        test_random_method_routing,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        print(f"\n{test_func.__name__.replace('_', ' ').title()}:")
        try:
            if test_func():
                passed += 1
                print("  ✅ PASSED")
            else:
                print("  ❌ FAILED")
        except Exception as e:
            print(f"  ❌ FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! Random method integration is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
